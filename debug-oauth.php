<?php
/**
 * Debug OAuth Setup - Check if OAuth is properly configured
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin privileges required.');
}

echo "<h2>LN Reader OAuth Debug Information</h2>";

// 1. Check if GoogleOAuth class exists
echo "<h3>1. Class and File Check</h3>";
if (class_exists('LNReader_GoogleOAuth')) {
    echo "<p>✅ <strong>LNReader_GoogleOAuth class loaded</strong></p>";
    
    $oauth = new LNReader_GoogleOAuth();
    if ($oauth->is_configured()) {
        echo "<p>✅ <strong>Google OAuth is configured</strong></p>";
        echo "<p>Auth URL: <code>" . $oauth->get_auth_url() . "</code></p>";
    } else {
        echo "<p>❌ <strong>Google OAuth is NOT configured</strong></p>";
        echo "<p>Please configure Client ID and Secret in Settings → Google OAuth</p>";
    }
} else {
    echo "<p>❌ <strong>LNReader_GoogleOAuth class NOT found</strong></p>";
    echo "<p>Check if includes/GoogleOAuth.php exists and is properly included</p>";
}

// 2. Check rewrite rules
echo "<h3>2. Rewrite Rules Check</h3>";
$rewrite_rules = get_option('rewrite_rules');
if (isset($rewrite_rules['^oauth/google/callback/?$'])) {
    echo "<p>✅ <strong>OAuth rewrite rule exists</strong></p>";
    echo "<p>Rule: <code>^oauth/google/callback/?$ → " . $rewrite_rules['^oauth/google/callback/?$'] . "</code></p>";
} else {
    echo "<p>❌ <strong>OAuth rewrite rule NOT found</strong></p>";
    echo "<p>Available rules:</p>";
    echo "<pre>";
    foreach ($rewrite_rules as $pattern => $replacement) {
        if (strpos($pattern, 'oauth') !== false || strpos($replacement, 'oauth') !== false) {
            echo "$pattern → $replacement\n";
        }
    }
    echo "</pre>";
}

// 3. Check query vars
echo "<h3>3. Query Variables Check</h3>";
global $wp;
if (in_array('ln_oauth_callback', $wp->public_query_vars)) {
    echo "<p>✅ <strong>Query variable 'ln_oauth_callback' is registered</strong></p>";
} else {
    echo "<p>❌ <strong>Query variable 'ln_oauth_callback' is NOT registered</strong></p>";
}

// 4. Check functions
echo "<h3>4. Function Check</h3>";
$functions_to_check = [
    'ln_reader_init_google_oauth',
    'ln_reader_add_oauth_query_vars',
    'ln_reader_handle_oauth_callback',
    'ln_reader_handle_google_oauth_callback',
    'ln_reader_get_google_oauth_url',
    'ln_reader_is_google_oauth_enabled'
];

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo "<p>✅ <strong>$function exists</strong></p>";
    } else {
        echo "<p>❌ <strong>$function NOT found</strong></p>";
    }
}

// 5. Check hooks
echo "<h3>5. WordPress Hooks Check</h3>";
$hooks_to_check = [
    'init' => 'ln_reader_init_google_oauth',
    'query_vars' => 'ln_reader_add_oauth_query_vars',
    'template_redirect' => 'ln_reader_handle_oauth_callback'
];

foreach ($hooks_to_check as $hook => $function) {
    if (has_action($hook, $function)) {
        echo "<p>✅ <strong>Hook '$hook' → '$function' is registered</strong></p>";
    } else {
        echo "<p>❌ <strong>Hook '$hook' → '$function' is NOT registered</strong></p>";
    }
}

// 6. Test URL generation
echo "<h3>6. URL Test</h3>";
$callback_url = home_url('/oauth/google/callback');
echo "<p><strong>Expected callback URL:</strong> <code>$callback_url</code></p>";

if (function_exists('ln_reader_get_google_oauth_url')) {
    $auth_url = ln_reader_get_google_oauth_url();
    if ($auth_url) {
        echo "<p><strong>Generated auth URL:</strong> <code>" . substr($auth_url, 0, 100) . "...</code></p>";
    } else {
        echo "<p>❌ <strong>Could not generate auth URL</strong></p>";
    }
}

// 7. Check .htaccess
echo "<h3>7. .htaccess Check</h3>";
$htaccess_file = ABSPATH . '.htaccess';
if (file_exists($htaccess_file)) {
    if (is_writable($htaccess_file)) {
        echo "<p>✅ <strong>.htaccess exists and is writable</strong></p>";
    } else {
        echo "<p>⚠️ <strong>.htaccess exists but is NOT writable</strong></p>";
        echo "<p>This might prevent rewrite rules from being updated.</p>";
    }
} else {
    echo "<p>❌ <strong>.htaccess file does not exist</strong></p>";
    echo "<p>WordPress might not be able to create rewrite rules.</p>";
}

// 8. Permalink structure
echo "<h3>8. Permalink Structure</h3>";
$permalink_structure = get_option('permalink_structure');
if (empty($permalink_structure)) {
    echo "<p>❌ <strong>Permalinks are set to 'Plain'</strong></p>";
    echo "<p>OAuth callbacks require pretty permalinks. Please go to Settings → Permalinks and choose any structure other than 'Plain'.</p>";
} else {
    echo "<p>✅ <strong>Permalink structure:</strong> <code>$permalink_structure</code></p>";
}

echo "<hr>";
echo "<h3>🛠️ Recommended Actions</h3>";
echo "<ol>";
echo "<li><strong>Run flush-rewrites.php</strong> to fix rewrite rules</li>";
echo "<li><strong>Go to Settings → Permalinks</strong> and click 'Save Changes'</li>";
echo "<li><strong>Configure Google OAuth</strong> in Settings → Google OAuth if not done</li>";
echo "<li><strong>Test the login</strong> after fixing the issues above</li>";
echo "</ol>";

echo "<p><a href='" . admin_url('options-general.php?page=ln-reader-google-oauth') . "'>Configure Google OAuth</a> | ";
echo "<a href='" . admin_url('options-permalink.php') . "'>Permalink Settings</a> | ";
echo "<a href='" . home_url('/login') . "'>Test Login</a></p>";
?>
