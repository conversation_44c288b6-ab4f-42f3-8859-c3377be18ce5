<?php
/**
 * Flush rewrite rules for Google OAuth
 * Run this file once after implementing OAuth to ensure callback URLs work
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin privileges required.');
}

echo "<h2>LN Reader OAuth Rewrite Rules Fix</h2>";

// Add the rewrite rule manually first
add_rewrite_rule('^oauth/google/callback/?$', 'index.php?ln_oauth_callback=google', 'top');

// Flush rewrite rules
flush_rewrite_rules(true);

echo "<p>✅ <strong>Rewrite rules added and flushed successfully!</strong></p>";
echo "<p>OAuth callback URL should now work: <code>" . home_url('/oauth/google/callback') . "</code></p>";

// Test if the query var is registered
global $wp;
if (in_array('ln_oauth_callback', $wp->public_query_vars)) {
    echo "<p>✅ <strong>Query variable 'ln_oauth_callback' is registered</strong></p>";
} else {
    echo "<p>❌ <strong>Query variable 'ln_oauth_callback' is NOT registered</strong></p>";
    echo "<p>This might cause issues. Please check functions.php.</p>";
}

// Show current rewrite rules (for debugging)
$rewrite_rules = get_option('rewrite_rules');
if (isset($rewrite_rules['^oauth/google/callback/?$'])) {
    echo "<p>✅ <strong>OAuth rewrite rule found in database</strong></p>";
} else {
    echo "<p>❌ <strong>OAuth rewrite rule NOT found in database</strong></p>";
    echo "<p>Trying to add it again...</p>";

    // Force add the rule
    global $wp_rewrite;
    $wp_rewrite->add_rule('^oauth/google/callback/?$', 'index.php?ln_oauth_callback=google', 'top');
    $wp_rewrite->flush_rules(true);

    echo "<p>✅ <strong>Forced rewrite rule addition complete</strong></p>";
}

echo "<hr>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Try the Google OAuth login again</li>";
echo "<li>If still getting 404, go to Settings → Permalinks and click 'Save Changes'</li>";
echo "<li>Check if your .htaccess file is writable</li>";
echo "<li>Delete this file after testing</li>";
echo "</ol>";

echo "<p><a href='" . home_url('/login') . "'>← Test Google OAuth Login</a></p>";
?>
