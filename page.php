<?php get_header(); ?>

<div class="row">
        <div class="col-12">
            <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
                <article class="page-content">
                    <header class="page-header mb-4">
                        <h1 class="page-title"><?php the_title(); ?></h1>
                        <?php if (get_the_excerpt()) : ?>
                            <div class="page-excerpt text-muted">
                                <?php the_excerpt(); ?>
                            </div>
                        <?php endif; ?>
                    </header>

                    <div class="page-content-wrapper">
                        <?php
                        the_content();
                        
                        // If the page has multiple pages (using <!--nextpage--> tags)
                        wp_link_pages(array(
                            'before' => '<div class="page-links mt-4"><span class="page-links-title">' . __('Pages:', 'lnreader') . '</span>',
                            'after'  => '</div>',
                            'link_before' => '<span class="page-number">',
                            'link_after'  => '</span>',
                        ));
                        ?>
                    </div>

                    <?php if (get_edit_post_link()) : ?>
                        <footer class="page-footer mt-4">
                            <div class="edit-link">
                                <?php edit_post_link(__('Edit this page', 'lnreader'), '<span class="edit-link-wrapper"><i class="fas fa-edit"></i> ', '</span>'); ?>
                            </div>
                        </footer>
                    <?php endif; ?>
                </article>

                <?php
                // If comments are open or we have at least one comment, load up the comment template.
                if (comments_open() || get_comments_number()) :
                    echo '<div class="comments-section mt-5">';
                    comments_template();
                    echo '</div>';
                endif;
                ?>

            <?php endwhile; else : ?>
                <div class="alert alert-info">
                    <h2><?php _e('Page not found', 'lnreader'); ?></h2>
                    <p><?php _e('Sorry, the page you are looking for could not be found.', 'lnreader'); ?></p>
                    <a href="<?php echo home_url(); ?>" class="btn btn-primary">
                        <i class="fas fa-home"></i> <?php _e('Return to Home', 'lnreader'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

<style>
/* Page-specific styles */
.page-content {
    background: #fff;
    border-radius: 4px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    border: none;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.page-content:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.page-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
}

.page-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-family: 'Fira Sans', sans-serif;
}

.page-excerpt {
    font-size: 1.1rem;
    line-height: 1.6;
}

.page-content-wrapper {
    font-size: 1rem;
    line-height: 1.7;
    color: #333;
    font-family: 'Fira Sans', sans-serif;
}

.page-content-wrapper h1,
.page-content-wrapper h2,
.page-content-wrapper h3,
.page-content-wrapper h4,
.page-content-wrapper h5,
.page-content-wrapper h6 {
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.page-content-wrapper h1 { font-size: 2rem; }
.page-content-wrapper h2 { font-size: 1.75rem; }
.page-content-wrapper h3 { font-size: 1.5rem; }
.page-content-wrapper h4 { font-size: 1.25rem; }

.page-content-wrapper p {
    margin-bottom: 1rem;
}

.page-content-wrapper img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 1rem 0;
}

.page-content-wrapper blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6c757d;
}

.page-content-wrapper ul,
.page-content-wrapper ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.page-content-wrapper li {
    margin-bottom: 0.5rem;
}

.page-links {
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
}

.page-links-title {
    font-weight: 600;
    margin-right: 1rem;
}

.page-number {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0 0.25rem;
    background: #f8f9fa;
    border-radius: 4px;
    text-decoration: none;
}

.page-number:hover {
    background: #007bff;
    color: white;
}

.edit-link-wrapper {
    font-size: 0.875rem;
    color: #6c757d;
}

.edit-link-wrapper:hover {
    color: #007bff;
}



.comments-section {
    background: #fff;
    border-radius: 4px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .page-content {
        padding: 1.5rem;
    }
}

@media (max-width: 575.98px) {
    .page-content {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .page-content-wrapper h1 { font-size: 1.5rem; }
    .page-content-wrapper h2 { font-size: 1.375rem; }
    .page-content-wrapper h3 { font-size: 1.25rem; }
}
</style>

<?php get_footer(); ?>
